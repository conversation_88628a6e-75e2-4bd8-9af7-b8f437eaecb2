#!/usr/bin/env python3
"""
Test script to help format JSON correctly for the vital signs endpoint
"""

import json
import requests

def create_clean_payload():
    """Create a clean, properly formatted JSON payload"""
    
    # Your data in a clean format
    payload = {
        "user_id": "test08",
        "vital_signs": {
            "Glucose": 110,
            "SpO2": 92,
            "Temperature": 38.2,
            "ECG (Heart Rate)": 102,
            "Blood Pressure (Systolic)": 130,
            "Blood Pressure (Diastolic)": 85,
            "Waist Circumference": 98,
            "Weight (BMI)": 26.5,
            "Hepatitis B": "Positive",
            "HIV": "Positive",
            "Malaria": "Positive",
            "Widal Test": "Positive",
            "Lung Capacity": 2.1
        }
    }
    
    return payload

def validate_json(payload):
    """Validate that the JSON is properly formatted"""
    try:
        # Convert to JSON string and back to ensure it's valid
        json_str = json.dumps(payload, indent=2)
        parsed_back = json.loads(json_str)
        
        print("✅ JSON is valid!")
        print("Formatted JSON:")
        print(json_str)
        
        # Check for common issues
        json_bytes = json_str.encode('utf-8')
        print(f"\nJSON byte length: {len(json_bytes)}")
        
        # Check for non-breaking spaces
        if '\xa0' in json_str or '\u00a0' in json_str:
            print("⚠️ WARNING: Non-breaking spaces detected!")
        else:
            print("✅ No non-breaking spaces found")
            
        # Check for other problematic characters
        problematic_chars = ['\r', '\n\n\n', '\t\t']
        for char in problematic_chars:
            if char in json_str:
                print(f"⚠️ WARNING: Problematic character found: {repr(char)}")
        
        return True, json_str
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON is invalid: {e}")
        return False, None

def test_with_requests(payload, base_url="http://localhost:8000"):
    """Test the payload with the actual endpoint"""
    
    endpoint_url = f"{base_url}/vital-signs"
    
    print(f"\n=== Testing with endpoint: {endpoint_url} ===")
    
    try:
        # Use requests to send the payload
        response = requests.post(
            endpoint_url, 
            json=payload,  # This automatically handles JSON encoding
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"Success: {result.get('success', 'Unknown')}")
            print(f"Message: {result.get('message', 'No message')}")
            if result.get('severity'):
                print(f"Severity: {result.get('severity')}")
            if result.get('suggest_consultation'):
                print("🩺 Consultation recommended")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error details: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Response text: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the server is running.")
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Request failed: {e}")

def create_curl_command(payload, base_url="http://localhost:8000"):
    """Generate a curl command for testing"""
    
    json_str = json.dumps(payload)
    endpoint_url = f"{base_url}/vital-signs"
    
    curl_command = f"""curl -X POST "{endpoint_url}" \\
  -H "Content-Type: application/json" \\
  -d '{json_str}'"""
    
    print(f"\n=== Curl Command ===")
    print(curl_command)
    
    # Also create a version with escaped quotes for Windows
    escaped_json = json_str.replace('"', '\\"')
    windows_curl = f'curl -X POST "{endpoint_url}" -H "Content-Type: application/json" -d "{escaped_json}"'
    
    print(f"\n=== Windows Curl Command ===")
    print(windows_curl)

if __name__ == "__main__":
    print("JSON Format Validator for Vital Signs Endpoint")
    print("=" * 60)
    
    # Create the payload
    payload = create_clean_payload()
    
    # Validate the JSON
    is_valid, json_str = validate_json(payload)
    
    if is_valid:
        # Test with requests (if server is running)
        test_with_requests(payload)
        
        # Generate curl commands
        create_curl_command(payload)
        
        print(f"\n=== Copy-Paste Ready JSON ===")
        print("You can copy this JSON and paste it directly:")
        print(json_str)
        
    print("\n" + "=" * 60)
    print("Validation completed!")
